"use client";
import React, { useEffect, useState } from "react";
import Loader from "@/app/components/common/Loader";
import {
  BookingDetails,
  Option,
  UpdateInGameNameResponse,
} from "@/app/types/CommonComponent.types";
import api from "@/app/utils/axiosInstance";
import { notFound, useParams } from "next/navigation";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import UpdateInGameNameForm from "@/app/components/updateInGameNameForm";
import { formatDate } from "@/app/utils/helper";
import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import GameInfoCard from "@/app/components/gameInfoCard";
import RaiseRequestForm from "@/app/components/raiseRequestForm";
import { Modal } from "@/app/components/modal";
import StartGameModal from "@/app/components/startGameModal";
import withAuth from "@/app/utils/withAuth";
import ServerError from "@/app/components/common/ServerError";

const TournamentDetailspage = () => {
  const [bookingDetails, setBookingDetails] = useState<BookingDetails | null>(
    null
  );
  const [inGameNameRes, setInGameNameRes] =
    useState<UpdateInGameNameResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showPriceBreakup, setShowPriceBreakup] = useState(false);
  const [showRaiseRequestModal, setShowRaiseRequestModal] = useState(false);
  const [showGameDetailsModal, setShowGameDetailsModal] = useState(false);
  const [options, setOptions] = useState<Option[]>([]);
  const [error, setError] = useState<any>(null);
  const params = useParams<{ tournamentId: string }>();
  const { tournamentId } = params;

  const getBookingDetails = async () => {
    try {
      const response = await api.get(
        API_ENDPOINTS.GET_BOOKING_DETAILS(tournamentId)
      );
      if (response.status === 200) {
        setBookingDetails(response?.data?.data);
        setInGameNameRes(response?.data?.data?.in_game_name);
      }
    } catch (error: any) {
      setError(error);
    } finally {
      setIsLoading(false);
    }
  };
  const fetchOptionsData = async () => {
    try {
      const optionsResponse = await api.get(
        API_ENDPOINTS.RAISE_REQUEST_OPTIONS
      );
      if (optionsResponse.status === 200) {
        setOptions(optionsResponse?.data?.data);
      }
    } catch (error: any) {
      console.log(error);
    }
  };
  useEffect(() => {
    getBookingDetails();
    fetchOptionsData();
  }, []);

  const handleFormSuccess = (response: UpdateInGameNameResponse) => {
    setInGameNameRes(response);
    setBookingDetails({
      ...bookingDetails,
      ...response,
    } as BookingDetails);
  };

  // const onRefundClick = () => {
  //   setShowRaiseRequestModal(true);
  // };

  if (error?.response?.status === 500) {
    return <ServerError />;
  }

  if (error?.response?.status === 404) {
    return notFound();
  }

  return (
    <div className="p-8">
      {isLoading && (
        <div className="h-[50vh] flex justify-center items-center w-[800px]">
          <Loader />
        </div>
      )}
      {!isLoading && bookingDetails && (
        <div className="max-w-[900px]">
          <h1 className="text-2xl font-bold text-white">
            <span className="text-2xl font-bold text-white">
              {bookingDetails?.tournament?.name}
            </span>{" "}
            :{" "}
            <span className="text-2xl font-bold text-white">
              {bookingDetails?.tournament?.description}
            </span>
          </h1>
          <div className="flex flex-col gap-6 mt-8">
            <GameInfoCard
              tournamentDetails={bookingDetails?.tournament}
              showJoinButton={false}
            />
            <div className="bg-[#141517] rounded-[30px] border border-[#707070] text-white p-6">
              <div>
                <div className="grid grid-cols-2 items-center">
                  <h3 className="text-xl font-semibold text-white h-fit">
                    Booking Details
                  </h3>
                  <div
                    className={`flex  items-end ${
                      bookingDetails?.time_slot?.is_cancelled
                        ? "justify-between"
                        : "justify-end"
                    }`}
                  >
                    {bookingDetails?.time_slot?.is_cancelled && (
                      <div className="flex flex-col  h-fit ">
                        <span className="text-white font-semibold text-lg">
                          Cancellation Reason
                        </span>
                        <span className="text-white font-medium text-base">
                          {bookingDetails?.time_slot?.cancel_reason}
                        </span>
                      </div>
                    )}
                    <div className="flex flex-col items-center gap-2">
                      <div
                        className={`flex items-center gap-2 border px-3 py-1 rounded ${
                          bookingDetails?.status.toLowerCase() === "booked"
                            ? "border-gray-500 bg-gray-500"
                            : bookingDetails?.status.toLowerCase() ===
                              "cancelled"
                            ? "border-red-500 bg-red-500"
                            : bookingDetails?.status.toLowerCase() ===
                              "completed"
                            ? "border-green-500 bg-green-500"
                            : ""
                        }`}
                      >
                        <span className="font-bold text-white">
                          {bookingDetails?.status}
                        </span>
                      </div>
                      {/* {bookingDetails?.status.toLowerCase() === "cancelled" &&
                        bookingDetails?.payment !== null && (
                          <div className="px-3 py-1 border-gray-100 border text-white rounded-md w-full hover:bg-[#c9ff88] hover:text-[#131517] transition-all">
                            <button
                              className="text-sm font-semibold"
                              onClick={onRefundClick}
                            >
                              Create Refund Request
                            </button>
                          </div>
                        )} */}
                    </div>
                  </div>
                </div>
                <div className="mt-3 mb-6">
                  <div className=" grid grid-cols-2 py-2 px-5 rounded gap-y-4">
                    <div className="space-y-1">
                      <p className="text-white">Tournament Name</p>
                      <p className="font-bold text-white">
                        {bookingDetails?.tournament?.name}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-white text-base">Slot</p>
                      <p className="font-bold text-white">
                        <span className="font-semibold text-base">
                          <span>
                            {bookingDetails?.time_slot?.formatted_time},{" "}
                          </span>
                          {formatDate(bookingDetails?.tournament?.date)}
                        </span>
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-white">Booking ID</p>
                      <p className="font-bold text-white">
                        {bookingDetails?.booking_id}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-white">Booked On</p>
                      <p className="font-bold text-white">
                        {bookingDetails?.created_at.split("T")[0]}
                      </p>
                    </div>
                    {inGameNameRes && (
                      <div className="space-y-1">
                        <p className="text-white">In Game Name</p>
                        <p className="font-bold text-white">
                          {bookingDetails?.in_game_name}
                        </p>
                      </div>
                    )}
                    {inGameNameRes && (
                      <div className="space-y-1">
                        <p className="text-white">Game Link</p>
                        <button
                          className="justify-center rounded-md bg-red-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500"
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowGameDetailsModal(true);
                          }}
                        >
                          Start game
                        </button>
                      </div>
                    )}
                  </div>
                </div>
                {!inGameNameRes && (
                  <>
                    <hr className="bg-gray-800 h-0.5 my-5" />
                    <div className="mb-6 w-3/5 px-5">
                      <UpdateInGameNameForm
                        bookingId={bookingDetails?.booking_id}
                        onSuccess={handleFormSuccess}
                        tournamentName={bookingDetails?.tournament?.name}
                      />
                    </div>
                  </>
                )}
              </div>

              <div className="mb-5">
                <h3 className="text-xl font-semibold text-white">
                  Winning Details
                </h3>
                <div className="mt-4 px-5">
                  <div className="grid grid-cols-2 gap-x-8">
                    <div>
                      <span className="font-medium text-base text-white">
                        Result
                      </span>
                    </div>
                    <div>
                      <span className="font-medium text-base text-white">
                        {bookingDetails?.result ?? "NA"}
                      </span>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-x-8">
                    <div>
                      <span className="font-medium text-base text-white">
                        Amount
                      </span>
                    </div>
                    <div>
                      <span className="font-medium text-base text-white">
                        ₹
                        {Number(bookingDetails?.amount).toLocaleString("en-IN")}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* payment details */}
              <div>
                <h3 className="text-xl font-semibold text-white">
                  Payment Details
                </h3>
                {bookingDetails?.payment ? (
                  <div className="mt-5 px-5">
                    <div className="grid grid-cols-2 gap-x-8">
                      <div>
                        <span className="font-medium text-base text-white">
                          Payment Mode
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-base text-white">
                          {bookingDetails?.payment?.payment_method}
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-x-8">
                      <div>
                        <span className="font-medium text-base text-white">
                          Payment Date
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-base text-white">
                          {bookingDetails?.payment?.created_at?.split("T")[0]}
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-x-8">
                      <div>
                        <span className="font-medium text-base text-white">
                          Total Amount
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-base text-white">
                          ₹
                          {Number(
                            bookingDetails?.payment?.amount
                          )?.toLocaleString("en-IN")}
                        </span>
                      </div>
                    </div>
                    <div className=" grid grid-cols-2 my-1 gap-x-8">
                      <div></div>
                      <button
                        onClick={() => setShowPriceBreakup(!showPriceBreakup)}
                        className="text-red-500 flex items-center gap-1 text-start"
                      >
                        {showPriceBreakup ? "Hide Breakup" : "Show Breakup"}
                        {showPriceBreakup ? (
                          <ChevronUpIcon
                            aria-hidden="true"
                            className=" h-4 w-4 text-red-500 text-semibold"
                          />
                        ) : (
                          <ChevronDownIcon
                            aria-hidden="true"
                            className=" h-4 w-4 text-red-500 text-semibold"
                          />
                        )}
                      </button>
                    </div>
                    {showPriceBreakup && (
                      <div>
                        <div className="grid grid-cols-2 gap-x-8">
                          <div>
                            <span className="font-medium text-base text-white">
                              A.Join Price
                            </span>
                          </div>
                          <div>
                            <span className="font-medium text-base text-white">
                              ₹
                              {Number(
                                bookingDetails?.tournament?.payment?.join_price
                              )?.toLocaleString("en-IN")}
                            </span>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-x-8">
                          <div>
                            <span className="font-medium text-base text-white">
                              B.Platform Fees (2% of A)
                            </span>
                          </div>
                          <div>
                            <span className="font-medium text-base text-white">
                              ₹
                              {bookingDetails?.tournament?.payment?.platform_fees?.toLocaleString(
                                "en-IN"
                              )}
                            </span>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-x-8">
                          <div>
                            <span className="font-medium text-base text-white">
                             C.Tax Amount (GST 28% of A+B)
                            </span>
                          </div>
                          <div>
                            <span className="font-medium text-base text-white">
                              ₹
                              {bookingDetails?.tournament?.payment?.tax_amount?.toLocaleString(
                                "en-IN"
                              )}
                            </span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-x-8">
                          <div>
                            <span className="font-medium text-base text-white">
                              D.Final Amount (A+B+C)
                            </span>
                          </div>
                          <div>
                            <span className="font-medium text-base text-white">
                              ₹
                              {bookingDetails?.tournament?.payment?.final_amount?.toLocaleString(
                                "en-IN"
                              )}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="mt-3 px-5">
                    <span>No details to be shown, free booking.</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
      <Modal
        modalOpen={showRaiseRequestModal}
        handleModalOpen={() => setShowRaiseRequestModal(false)}
      >
        <RaiseRequestForm
          handleModal={setShowRaiseRequestModal}
          options={options}
          prefillData={{
            issue: "Refunds & Cancellation",
            booking_id: bookingDetails?.booking_id as string,
          }}
        />
      </Modal>
      <Modal
        modalOpen={showGameDetailsModal}
        handleModalOpen={() => setShowGameDetailsModal(false)}
      >
        <StartGameModal
          game_link={bookingDetails?.game_link as string}
          room_id={bookingDetails?.room_id as string}
          room_password={bookingDetails?.room_password as string}
          createdBy={bookingDetails?.tournament?.created_by}
        />
      </Modal>
    </div>
  );
};

export default withAuth(TournamentDetailspage);
