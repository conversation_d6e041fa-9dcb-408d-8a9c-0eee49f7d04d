import { useEffect, useState } from "react";
import PartnerCard from "../partnerCard";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import api from "@/app/utils/axiosInstance";
import {
  YoutubePartner,
  YoutubePartnerResponse,
} from "@/app/types/CommonComponent.types";
import { Modal } from "../modal";
import Pagination from "../pagination";
import Loader from "../common/Loader";

const GamyDayPartners = () => {
  const [youtubePartners, setYoutubePartners] =
    useState<YoutubePartnerResponse>({} as YoutubePartnerResponse);
  const [homePagePartners, setHomePagePartners] = useState<YoutubePartner[]>(
    []
  );
  const [showAllPartners, setShowAllPartners] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  const totalPages = Math.ceil(youtubePartners?.count / itemsPerPage);

  const fetchYoutubePartners = async () => {
    setLoading(true);
    try {
      const response = await api.get(
        API_ENDPOINTS.GET_YOUTUBE_PARTNERS(currentPage)
      );
      if (response.status === 200) {
        setYoutubePartners(response?.data);
        if (currentPage === 1) {
          setHomePagePartners(response?.data?.results);
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchYoutubePartners();
  }, [currentPage]);

  return (
    <>
      {homePagePartners?.length !== 0 ? (
        <section className="pt-6 px-4">
          <div className="flex justify-between items-center">
            {/* <h2 className="text-3xl font-bold mb-6 text-white">
              Youtube Partners
            </h2> */}
            <div
              className="text-base w-full text-right mb-4 font-semibold text-white underline cursor-pointer hover:text-[#c9ff88]"
              onClick={() => setShowAllPartners(true)}
            >
              Youtube Partners
            </div>
          </div>
          <div className="flex flex-wrap gap-10">
            {homePagePartners?.slice(0, 16)?.map((partner) => (
              <PartnerCard key={partner?.username} partner={partner} />
            ))}
          </div>
          <Modal
            modalOpen={showAllPartners}
            handleModalOpen={() => setShowAllPartners(false)}
          >
            <div className="w-[800px] h-[500px] flex flex-col">
              <h2 className="text-3xl font-bold mb-6 text-white">
                Youtube Partners
              </h2>
              {loading && <Loader />}
              <div className="flex-1">
                <div className="flex flex-wrap gap-10">
                  {!loading &&
                    youtubePartners?.results?.map((partner) => (
                      <PartnerCard key={partner?.username} partner={partner} />
                    ))}
                </div>
              </div>
              {youtubePartners?.count > 0 && (
                <div className="border-t mt-4 pt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={(page) => setCurrentPage(page)}
                  />
                </div>
              )}
            </div>
          </Modal>
        </section>
      ) : (
        <></>
      )}
    </>
  );
};

export default GamyDayPartners;
